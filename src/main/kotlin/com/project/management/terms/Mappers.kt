package com.project.management.terms

import com.project.management.users.User

fun TermPostRequest.toModel(performer: User, termsGroup: TermsGroup): Term {
    return Term(
        name = name,
        description = description,
        termsGroupId = termsGroup.id!!,
        termsGroup = termsGroup,
        organizationId = performer.organizationId,
        createdBy = performer.id!!,
        updatedBy = performer.id!!,
    )
}

fun TermsGroupPostRequest.toModel(performer: User): TermsGroup {
    return TermsGroup(
        name = name,
        description = description ?: "",
        organizationId = performer.organizationId,
        createdBy = performer.id!!,
        updatedBy = performer.id!!,
    )
}