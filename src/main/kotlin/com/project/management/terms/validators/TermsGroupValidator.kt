package com.project.management.terms.validators

import com.project.management.common.exceptions.BusinessException
import com.project.management.terms.TermsGroup
import com.project.management.terms.repositories.TermsGroupsRepository
import org.springframework.stereotype.Component

@Component
class TermsGroupValidator(
    private val termsGroupsRepository: TermsGroupsRepository
) {
    fun validateTermsGroupExistsByIdAndOrganizationId(termsGroupId: Long, organizationId: Long): TermsGroup {
        return termsGroupsRepository.findByIdAndOrganizationId(termsGroupId, organizationId)
            ?: throw BusinessException.NotFoundException("TermsGroup with id $termsGroupId not found")
    }
}