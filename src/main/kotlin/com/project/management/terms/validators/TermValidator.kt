package com.project.management.terms.validators

import com.project.management.common.exceptions.BusinessException
import com.project.management.terms.Term
import com.project.management.terms.repositories.TermsRepository
import org.springframework.stereotype.Component

@Component
class TermValidator(
    private val termsRepository: TermsRepository
) {

    fun validateTermExistsByIdAndOrganizationId(termId: Long, organizationId: Long): Term {
        return termsRepository.findByIdAndOrganizationId(termId, organizationId)
            ?: throw BusinessException.NotFoundException("Term with id $termId not found")
    }
}