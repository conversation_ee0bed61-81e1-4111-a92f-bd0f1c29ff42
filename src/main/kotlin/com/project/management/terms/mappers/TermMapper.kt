package com.project.management.terms.mappers

import com.project.management.common.annotation.MappingOrganizationEntity
import com.project.management.terms.requests.TermRequestDto
import com.project.management.terms.Term
import com.project.management.terms.TermsGroup
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy
import java.time.ZonedDateTime

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface TermMapper {
    @MappingOrganizationEntity
    @Mapping(target = "name", source = "termRequestDto.name")
    @Mapping(target = "description", source = "termRequestDto.description")
    @Mapping(target = "termsGroup", source = "termsGroup")
    fun toTerm(
        termRequestDto: TermRequestDto,
        createdAt: ZonedDateTime = ZonedDateTime.now(),
        updatedAt: ZonedDateTime = createdAt,
        termsGroup: TermsGroup,
        termsGroupId: Long,
        organizationId: Long,
        userId: Long?,
        version: Long
    ): Term
}