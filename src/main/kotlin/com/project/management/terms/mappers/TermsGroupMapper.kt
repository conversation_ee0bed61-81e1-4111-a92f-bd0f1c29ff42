package com.project.management.terms.mappers

import com.project.management.common.annotation.MappingOrganizationEntity
import com.project.management.terms.TermPostRequest
import com.project.management.terms.TermsGroupPostRequest
import com.project.management.terms.TermsGroup
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy
import java.time.ZonedDateTime

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface TermsGroupMapper {
    @MappingOrganizationEntity
    @Mapping(target = "name", source = "termsGroupRequestDto.name")
    @Mapping(target = "description", source = "termsGroupRequestDto.description")
    fun toTermsGroup(
        termsGroupRequestDto: TermsGroupPostRequest,
        version: Long,
        createdAt: ZonedDateTime = ZonedDateTime.now(),
        updatedAt: ZonedDateTime = createdAt,
        organizationId: Long,
        userId: Long?
    ): TermsGroup

    fun toTermRequest(
        termsGroupRequestDto: TermsGroupPostRequest,
    ): TermPostRequest
}