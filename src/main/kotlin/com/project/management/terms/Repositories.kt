package com.project.management.terms

import com.project.management.common.entity.OrganizationId
import com.project.management.common.exceptions.BusinessException
import com.project.management.common.utility.autowired
import com.project.management.common.utility.filterDeleted
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

object TermRepository {
    private val repo: TermRepo by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<Term> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getAllByTermsGroup(organizationId: OrganizationId, termsGroupId: Long): List<Term> = filterDeleted {
            repo.findAllByOrganizationIdAndTermsGroupId(organizationId.value, termsGroupId)
        }

        fun getById(organizationId: OrganizationId, id: Long): Term? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }
    }

    object Mutate {
        fun save(term: Term): Term {
            return repo.save(term)
        }
    }

    object Validate {
        fun validateExistsByIdAndOrganizationId(termId: Long, organizationId: Long): Term {
            return repo.findByIdAndOrganizationId(termId, organizationId)
                ?: throw BusinessException.NotFoundException("Term with id $termId not found")
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(TERM_DELETED_FILTER, block)
}

object TermsGroupRepository {
    private val repo: TermsGroupRepo by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<TermsGroup> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getById(organizationId: OrganizationId, id: Long): TermsGroup? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }
    }

    object Mutate {
        fun save(termsGroup: TermsGroup): TermsGroup {
            return repo.save(termsGroup)
        }
    }

    object Validate {
        fun validateExistsByIdAndOrganizationId(termsGroupId: Long, organizationId: Long): TermsGroup {
            return repo.findByIdAndOrganizationId(termsGroupId, organizationId)
                ?: throw BusinessException.NotFoundException("TermsGroup with id $termsGroupId not found")
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(TERMS_GROUP_DELETED_FILTER, block)
}

@Repository
private interface TermRepo : JpaRepository<Term, Long> {
    fun findAllByOrganizationId(organizationId: Long): List<Term>
    fun findAllByOrganizationIdAndTermsGroupId(organizationId: Long, termsGroupId: Long): List<Term>
    fun findByIdAndOrganizationId(id: Long, organizationId: Long): Term?
}

@Repository
private interface TermsGroupRepo : JpaRepository<TermsGroup, Long> {
    fun findByIdAndOrganizationId(id: Long, organizationId: Long): TermsGroup?
    fun findAllByOrganizationId(organizationId: Long): List<TermsGroup>
}