package com.project.management.terms.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.terms.requests.TermsGroupRequestDto
import com.project.management.terms.TermsGroup
import com.project.management.terms.mappers.TermsGroupMapper
import com.project.management.terms.repositories.TermsGroupsRepository
import com.project.management.terms.requests.PatchTermsGroupRequestDto
import com.project.management.terms.validators.TermsGroupValidator
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class TermsGroupsService(
    private val termsGroupsRepository: TermsGroupsRepository,
    private val termsGroupMapper: TermsGroupMapper,
    private val termsGroupValidator: TermsGroupValidator,
    private val currentUser: CurrentUserConfig
) {
    fun getAll(): List<TermsGroup> {
        val user = currentUser.getCurrentUser()
        val termsGroups = termsGroupsRepository.findAllByOrganizationId(user.organizationId)
        return termsGroups
    }

    @Transactional
    fun create(termsGroupRequestDto: TermsGroupRequestDto): TermsGroup {
        val user = currentUser.getCurrentUser()
        var termsGroup = termsGroupMapper.toTermsGroup(
            termsGroupRequestDto,
            organizationId = user.organizationId,
            userId = user.id,
            version = 1
        )
        termsGroup = termsGroupsRepository.save(termsGroup)
        return termsGroup
    }

    @Transactional
    fun update(request: PatchTermsGroupRequestDto, termsGroupId: Long): TermsGroup {
        val loggedInUser = currentUser.getCurrentUser()
        val termsGroup = termsGroupValidator.validateTermsGroupExistsByIdAndOrganizationId(
            termsGroupId = termsGroupId,
            organizationId = loggedInUser.organizationId
        )
        if (termsGroup.version != request.version) {
            throw BusinessException.ConflictException()
        }

        val updatedTermsGroup = termsGroup.copy(
            name = request.name ?: termsGroup.name,
            description = request.description ?: termsGroup.description,
            updatedBy = loggedInUser.id!!
        )
        return termsGroupsRepository.save(updatedTermsGroup)
    }
}