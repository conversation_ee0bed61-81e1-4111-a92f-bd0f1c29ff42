package com.project.management.terms.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.terms.TermPostRequest
import com.project.management.terms.Term
import com.project.management.terms.toModel
import com.project.management.terms.repositories.TermsRepository
import com.project.management.terms.TermPatchRequest
import com.project.management.terms.validators.TermValidator
import com.project.management.terms.validators.TermsGroupValidator
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class TermsService(
    private val termsRepository: TermsRepository,
    private val currentUser: CurrentUserConfig,
    private val termsGroupValidator: TermsGroupValidator,
    private val termValidator: TermValidator
) {

    fun getAll(): List<Term> {
        val user = currentUser.getCurrentUser()
        val terms = termsRepository.findAllByOrganizationId(user.organizationId)
        return terms
    }

    fun getAll(termsGroupId: Long): List<Term> {
        val user = currentUser.getCurrentUser()
        val terms = termsRepository.findAllByOrganizationIdAndTermsGroupId(
            user.organizationId,
            termsGroupId
        )
        return terms
    }

    @Transactional
    fun create(termRequestDto: TermPostRequest, termsGroupId: Long): Term {
        val user = currentUser.getCurrentUser()
        val termsGroup = termsGroupValidator.validateTermsGroupExistsByIdAndOrganizationId(
            termsGroupId,
            user.organizationId
        )
        var term = termRequestDto.toModel(user, termsGroup)
        term = termsRepository.save(term)
        return term
    }

    @Transactional
    fun edit(request: TermPatchRequest, termId: Long): Term {
        val loggedInUser = currentUser.getCurrentUser()
        val term = termValidator.validateTermExistsByIdAndOrganizationId(
            termId = termId,
            organizationId = loggedInUser.organizationId
        )
        if (term.version != request.version) throw BusinessException.ConflictException()

        val updatedTerm = term.copy(
            name = request.name ?: term.name,
            description = request.description ?: term.description,
            updatedBy = loggedInUser.id!!
        )
        return termsRepository.save(updatedTerm)
    }
}