package com.project.management.terms.repositories

import com.project.management.terms.Term
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface TermsRepository: JpaRepository<Term, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<Term>

    fun findAllByOrganizationIdAndTermsGroupId(organizationId: Long, termId: Long): List<Term>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): Term?
}