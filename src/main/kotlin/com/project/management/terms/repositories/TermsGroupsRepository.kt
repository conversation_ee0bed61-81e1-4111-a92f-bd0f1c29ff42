package com.project.management.terms.repositories

import com.project.management.terms.TermsGroup
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface TermsGroupsRepository: JpaRepository<TermsGroup, Long> {
    fun findByIdAndOrganizationId(id: Long, organizationId: Long): TermsGroup?
    fun findAllByOrganizationId(organizationId: Long): List<TermsGroup>
}