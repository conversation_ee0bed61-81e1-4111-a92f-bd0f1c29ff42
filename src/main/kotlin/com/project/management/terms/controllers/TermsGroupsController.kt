package com.project.management.terms.controllers

import com.project.management.terms.requests.TermsGroupRequestDto
import com.project.management.terms.TermsGroup
import com.project.management.terms.services.TermsGroupsService
import com.project.management.terms.requests.PatchTermsGroupRequestDto
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/terms/groups")
class TermsGroupsController(
    private val termsGroupsService: TermsGroupsService
) {
    @GetMapping
    fun getAll(): ResponseEntity<List<TermsGroup>> {
        return ResponseEntity.ok(termsGroupsService.getAll())
    }

    @PostMapping
    fun create(@RequestBody termsGroupRequestDto: TermsGroupRequestDto): ResponseEntity<TermsGroup> {
        return ResponseEntity.ok(termsGroupsService.create(termsGroupRequestDto))
    }

    @PatchMapping("/{termsGroupId}")
    fun update(
        @RequestBody request: PatchTermsGroupRequestDto,
        @PathVariable termsGroupId: Long
    ): ResponseEntity<TermsGroup> {
        return ResponseEntity.ok(termsGroupsService.update(request, termsGroupId))
    }
}