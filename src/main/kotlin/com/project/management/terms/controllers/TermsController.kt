package com.project.management.terms.controllers

import com.project.management.terms.TermPostRequest
import com.project.management.terms.Term
import com.project.management.terms.services.TermsService
import com.project.management.terms.TermPatchRequest
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/terms")
class TermsController(
    val termsService: TermsService
) {
    @GetMapping
    fun getAll(): ResponseEntity<List<Term>> {
        val terms = termsService.getAll()
        return ResponseEntity.ok(terms)
    }

    @GetMapping("/groups/{groupId}")
    fun getAll(@PathVariable groupId: Long): ResponseEntity<List<Term>> {
        val terms = termsService.getAll(groupId)
        return ResponseEntity.ok(terms)
    }

    @PostMapping("/groups/{groupId}")
    fun create(
        @PathVariable groupId: Long,
        @RequestBody termRequestDto: TermPostRequest,
    ): ResponseEntity<Term> {
        val term = termsService.create(termRequestDto, groupId)
        return ResponseEntity.ok(term)
    }

    @PatchMapping("{termId}")
    fun edit(
        @PathVariable termId: Long,
        @RequestBody request: TermPatchRequest,
    ): ResponseEntity<Term> {
        return ResponseEntity.ok(termsService.edit(request, termId))
    }
}